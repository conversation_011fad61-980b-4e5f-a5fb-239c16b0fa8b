<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>80.lv 导航插件测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            text-align: center;
        }
        .test-section {
            background: white;
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section h2 {
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        .improvement-list {
            background: #e8f5e8;
            border-left: 4px solid #4caf50;
            padding: 15px;
            margin: 15px 0;
        }
        .improvement-list h3 {
            color: #2e7d32;
            margin-top: 0;
        }
        .improvement-list ul {
            margin: 10px 0;
        }
        .improvement-list li {
            margin: 8px 0;
            color: #1b5e20;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 15px 0;
        }
        .highlight {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        .button {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: all 0.2s ease;
        }
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }
        .status {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 80.lv 导航插件测试页面</h1>
        <p>v3.6 终极修复版 - 彻底解决前一页按钮状态问题</p>
    </div>

    <div class="test-section">
        <h2>📋 测试总结</h2>
        <div class="improvement-list">
            <h3>✅ v3.6 终极修复</h3>
            <ul>
                <li><strong>恢复v2.7稳定选择器</strong>：从 <code>._3TaHD</code> 回退到 <code>*._2ZymR</code></li>
                <li><strong>简化状态检测逻辑</strong>：完全采用基于页码的简单判断</li>
                <li><strong>彻底解决误判问题</strong>：移除复杂的按钮存在性检查</li>
                <li><strong>保留增强功能</strong>：维持v3.5的超快速导航等功能</li>
                <li><strong>稳定可靠</strong>：结合v2.7的稳定性和v3.5的功能性</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>🔍 Playwright 测试发现</h2>
        <div class="highlight">
            <strong>关键发现：</strong>
            <ul>
                <li>第1页时：上一页按钮是 <code>&lt;button [disabled]&gt;</code></li>
                <li>第2页时：上一页按钮变成 <code>&lt;a href="/articles/"&gt;</code></li>
                <li>所有导航按钮都使用 <code>._3TaHD</code> 类</li>
                <li>分页容器使用 <code>ul._2pSuN</code></li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>⚙️ 配置更新</h2>
        <div class="code-block">
selectors: {
    // 更新前
    sitePrevButton: 'ul._2pSuN > li:first-child > *._2ZymR',
    siteNextButton: 'ul._2pSuN > li:last-child > *._2ZymR',

    // 更新后（基于Playwright测试）
    sitePrevButton: 'ul._2pSuN > li:first-child > ._3TaHD',
    siteNextButton: 'ul._2pSuN > li:last-child > ._3TaHD',
}
        </div>
    </div>

    <div class="test-section">
        <h2>🛠️ 函数改进</h2>
        <h3>isButtonDisabled() 函数</h3>
        <div class="code-block">
// 新增：区分button和a元素的检测逻辑
const isButtonElement = button.tagName.toLowerCase() === 'button';
const isLinkElement = button.tagName.toLowerCase() === 'a';

// button元素：检查disabled属性
if (isButtonElement && (button.disabled || button.hasAttribute('disabled'))) {
    return true;
}

// a元素：检查href属性
if (isLinkElement) {
    const href = button.getAttribute('href');
    if (!href || href.trim() === '' || href === '#') {
        return true;
    }
}
        </div>

        <h3>getSiteButtonStates() 函数</h3>
        <div class="code-block">
// 优先查找._3TaHD类的元素
const prevButton = firstLi.querySelector('._3TaHD') || firstLi.querySelector('button, a');
const nextButton = lastLi.querySelector('._3TaHD') || lastLi.querySelector('button, a');

// 更精确的状态检测
const isButtonElement = prevButton.tagName.toLowerCase() === 'button';
const isButtonDisabled = isButtonElement && (prevButton.disabled || prevButton.hasAttribute('disabled'));
        </div>
    </div>

    <div class="test-section">
        <h2>🎯 测试指南</h2>
        <div class="status success">
            <strong>✅ 推荐测试步骤：</strong>
        </div>
        <ol>
            <li>在 80.lv/articles 页面安装更新后的脚本</li>
            <li>检查控制台日志，确认版本为 v3.4</li>
            <li>验证在第1页时上一页按钮正确禁用</li>
            <li>点击下一页，验证上一页按钮变为可用</li>
            <li>测试上一页按钮功能是否正常工作</li>
            <li>测试快速跳转功能</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>📊 预期结果</h2>
        <div class="status success">
            <strong>成功指标：</strong>
            <ul>
                <li>上一页按钮状态检测准确</li>
                <li>按钮点击功能正常</li>
                <li>控制台日志显示正确的按钮类型和状态</li>
                <li>无JavaScript错误</li>
            </ul>
        </div>
    </div>

    <script>
        // 简单的测试脚本
        console.log('🚀 80.lv 导航插件测试页面已加载');
        console.log('📝 请在 80.lv/articles 页面测试实际功能');

        // 模拟一些测试按钮
        function simulateTest(testName) {
            console.log(`🧪 模拟测试: ${testName}`);
            alert(`测试 "${testName}" 已触发\n请查看控制台日志`);
        }
    </script>

    <div class="test-section">
        <h2>🔗 快速链接</h2>
        <button class="button" onclick="window.open('https://80.lv/articles', '_blank')">
            打开 80.lv Articles
        </button>
        <button class="button" onclick="simulateTest('按钮状态检测')">
            模拟状态检测
        </button>
        <button class="button" onclick="simulateTest('导航功能')">
            模拟导航测试
        </button>
    </div>

    <div class="test-section">
        <h2>📝 版本历史</h2>
        <ul>
            <li><strong>v3.4</strong> - 基于Playwright测试优化按钮状态检测</li>
            <li><strong>v3.3</strong> - 增加详细日志输出进行调试</li>
            <li><strong>v3.2</strong> - 改进按钮状态检测逻辑</li>
            <li><strong>v3.1</strong> - 添加超快速导航功能</li>
        </ul>
    </div>
</body>
</html>
